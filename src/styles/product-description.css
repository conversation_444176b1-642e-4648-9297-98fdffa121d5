/* Product Description Formatting Styles - Simple & Clean */

.formatted-content-container {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, 'Helvetica Neue', Arial, sans-serif;
  line-height: 1.6;
}

.formatted-content h2 {
  color: #1f2937 !important;
  font-size: 1.1rem !important;
  font-weight: 600 !important;
  margin-bottom: 1rem !important;
  margin-top: 0 !important;
}

.formatted-content p {
  color: #4b5563 !important;
  line-height: 1.6 !important;
  margin-bottom: 1rem !important;
  font-size: 0.95rem !important;
  text-align: left !important;
}

.formatted-content .intro-paragraph {
  font-size: 0.95rem !important;
  color: #4b5563 !important;
  font-weight: 400 !important;
  margin-bottom: 1rem !important;
}

.formatted-content .technical-paragraph {
  color: #4b5563 !important;
  font-weight: 400 !important;
}

.formatted-content .usage-paragraph {
  color: #4b5563 !important;
  font-weight: 400 !important;
}

.formatted-content .closing-paragraph {
  color: #4b5563 !important;
  font-weight: 400 !important;
  margin-bottom: 0 !important;
}

.formatted-content strong {
  color: #1f2937 !important;
  font-weight: 600 !important;
}

/* Dark mode styles */
.dark .formatted-content h2 {
  color: #f9fafb !important;
}

.dark .formatted-content p {
  color: #d1d5db !important;
}

.dark .formatted-content .intro-paragraph {
  color: #d1d5db !important;
}

.dark .formatted-content .technical-paragraph {
  color: #d1d5db !important;
}

.dark .formatted-content .usage-paragraph {
  color: #d1d5db !important;
}

.dark .formatted-content .closing-paragraph {
  color: #d1d5db !important;
}

.dark .formatted-content strong {
  color: #f9fafb !important;
}

/* Simple content styling */
.simple-content {
  color: #4b5563;
  line-height: 1.6;
  font-size: 0.95rem;
  white-space: pre-wrap;
}

.dark .simple-content {
  color: #d1d5db;
}

/* Additional info sections styling */
.simple-content strong,
.simple-content .section-header {
  color: #1f2937;
  font-weight: 600;
  display: block;
  margin-top: 1.5rem;
  margin-bottom: 0.5rem;
  font-size: 0.95rem;
}

.dark .simple-content strong,
.dark .simple-content .section-header {
  color: #f9fafb;
}

/* First section header should not have top margin */
.simple-content strong:first-child,
.simple-content .section-header:first-child {
  margin-top: 0;
}

/* Enhanced formatting for new structure */
.formatted-content-wrapper {
  line-height: 1.7;
}

.formatted-content-wrapper .section-header {
  color: #1f2937;
  font-weight: 600;
  display: block;
  margin-top: 1.5rem;
  margin-bottom: 0.75rem;
  font-size: 0.95rem;
  border-bottom: 1px solid #e5e7eb;
  padding-bottom: 0.25rem;
}

.dark .formatted-content-wrapper .section-header {
  color: #f9fafb;
  border-bottom-color: #374151;
}

/* First section header styling */
.formatted-content-wrapper .section-header:first-child {
  margin-top: 0;
}

/* Bullet point styling */
.formatted-content-wrapper .bullet-point {
  color: #4b5563;
  line-height: 1.6;
  margin-bottom: 0.5rem;
  padding-left: 0.5rem;
}

.dark .formatted-content-wrapper .bullet-point {
  color: #d1d5db;
}

/* Content line styling */
.formatted-content-wrapper .content-line {
  color: #4b5563;
  line-height: 1.6;
  margin-bottom: 0.5rem;
}

.dark .formatted-content-wrapper .content-line {
  color: #d1d5db;
}

/* Bullet points styling for simple content */
.simple-content {
  line-height: 1.7;
}

/* Ensure proper spacing between sections */
.simple-content p {
  margin-bottom: 1rem;
}

.simple-content p:last-child {
  margin-bottom: 0;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .formatted-content h2 {
    font-size: 1rem !important;
  }

  .formatted-content p {
    font-size: 0.9rem !important;
  }
}
