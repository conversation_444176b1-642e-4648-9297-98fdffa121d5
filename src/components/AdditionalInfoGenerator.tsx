'use client';

import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { toast } from 'sonner';
import { Lo<PERSON>, <PERSON>rk<PERSON>, Info, CheckCircle2 } from 'lucide-react';

interface AdditionalInfoSuggestion {
  category: string;
  title: string;
  description: string;
  priority: 'high' | 'medium' | 'low';
}

interface AdditionalInfoResponse {
  suggestions: AdditionalInfoSuggestion[];
  analysis: {
    productType: string;
    detectedFeatures: string[];
    recommendedSections: string[];
  };
}

interface AdditionalInfoGeneratorProps {
  productName: string;
  category: string;
  existingContent: string;
  onContentGenerated: (content: string) => void;
}

// Function to normalize whitespace and ensure consistent formatting
const normalizeWhitespace = (text: string): string => {
  if (!text) return '';

  return text
    // Normalize line breaks - maximum two consecutive line breaks for content, three for section separation
    .replace(/\n\s*\n\s*\n\s*\n+/g, '\n\n\n')
    // Remove trailing spaces from each line
    .replace(/[ \t]+$/gm, '')
    // Remove leading spaces from lines (except intentional indentation)
    .replace(/^\s+/gm, '')
    // Convert multiple spaces to single space (preserve line structure)
    .replace(/[ \t]+/g, ' ')
    // Remove spaces after line breaks
    .replace(/\n /g, '\n')
    // Ensure proper spacing around bullet points
    .replace(/\n•/g, '\n• ')
    .replace(/• +/g, '• ')
    // Clean up start and end
    .trim();
};

// Function to apply comprehensive text corrections (enhanced version matching main component)
const applyTextCorrections = (text: string): string => {
  if (!text) return '';

  let corrected = text;

  // Fix spacing issues
  corrected = corrected.replace(/\s+/g, ' '); // Multiple spaces to single space
  corrected = corrected.replace(/\s+([.,;:!?])/g, '$1'); // Remove space before punctuation
  corrected = corrected.replace(/([.,;:!?])([a-zA-Z])/g, '$1 $2'); // Add space after punctuation

  // Fix capitalization after punctuation
  corrected = corrected.replace(/([.!?])\s+([a-z])/g, (match, punct, letter) => {
    return punct + ' ' + letter.toUpperCase();
  });

  // Comprehensive Portuguese spelling corrections (matching main component)
  const corrections: { [key: string]: string } = {
    // Common double letter errors
    'qualidadde': 'qualidade',
    'funcionalidadde': 'funcionalidade',
    'velocidadde': 'velocidade',
    'capacidadde': 'capacidade',
    'seguranssa': 'segurança',
    'performancce': 'performance',
    'resistênccia': 'resistência',
    'eficiênccia': 'eficiência',

    // Accent errors
    'resistênte': 'resistente',
    'duravél': 'durável',
    'portátil': 'portátil',
    'práticó': 'prático',
    'económico': 'económico',
    'ergonómico': 'ergonómico',
    'automáticó': 'automático',
    'específicó': 'específico',

    // PT-BR vs PT-PT standardization (to PT-PT)
    'eletrônico': 'eletrónico',
    'eletrônicos': 'eletrónicos',
    'eletrônica': 'eletrónica',
    'electrónico': 'eletrónico',
    'electrónicos': 'eletrónicos',
    'electrónica': 'eletrónica',

    // Optimization variants
    'optimizar': 'otimizar',
    'optimizado': 'otimizado',
    'optimização': 'otimização',
    'optimizador': 'otimizador',

    // Technology terms
    'tecnológia': 'tecnologia',
    'tecnológico': 'tecnológico',
    'conectivídade': 'conectividade',
    'compatibilídade': 'compatibilidade',
    'funcionalídade': 'funcionalidade',

    // Common misspellings
    'característica': 'característica',
    'especificação': 'especificação',
    'recomendação': 'recomendação',
    'manutenção': 'manutenção',
    'instalação': 'instalação',
    'configuração': 'configuração'
  };

  // Apply spelling corrections
  Object.entries(corrections).forEach(([wrong, right]) => {
    const regex = new RegExp(`\\b${wrong}\\b`, 'gi');
    corrected = corrected.replace(regex, right);
  });

  // Portuguese grammar contractions
  corrected = corrected.replace(/\bde a\b/gi, 'da');
  corrected = corrected.replace(/\bde o\b/gi, 'do');
  corrected = corrected.replace(/\bde os\b/gi, 'dos');
  corrected = corrected.replace(/\bde as\b/gi, 'das');
  corrected = corrected.replace(/\bem o\b/gi, 'no');
  corrected = corrected.replace(/\bem a\b/gi, 'na');
  corrected = corrected.replace(/\bem os\b/gi, 'nos');
  corrected = corrected.replace(/\bem as\b/gi, 'nas');
  corrected = corrected.replace(/\bpor o\b/gi, 'pelo');
  corrected = corrected.replace(/\bpor a\b/gi, 'pela');
  corrected = corrected.replace(/\bpor os\b/gi, 'pelos');
  corrected = corrected.replace(/\bpor as\b/gi, 'pelas');

  // Ensure proper capitalization at start of sentences
  corrected = corrected.replace(/^\s*([a-z])/g, (match, letter) => letter.toUpperCase());

  // Fix capitalization after line breaks (for bullet points and new lines)
  corrected = corrected.replace(/\n\s*([a-z])/g, (match, letter) => match.replace(letter, letter.toUpperCase()));

  return corrected.trim();
};

const AdditionalInfoGenerator: React.FC<AdditionalInfoGeneratorProps> = ({
  productName,
  category,
  existingContent,
  onContentGenerated
}) => {
  const [suggestions, setSuggestions] = useState<AdditionalInfoSuggestion[]>([]);
  const [selectedSections, setSelectedSections] = useState<string[]>([]);
  const [isLoadingSuggestions, setIsLoadingSuggestions] = useState(false);
  const [isGeneratingContent, setIsGeneratingContent] = useState(false);
  const [analysis, setAnalysis] = useState<AdditionalInfoResponse['analysis'] | null>(null);
  const [hasAnalyzed, setHasAnalyzed] = useState(false);

  // Auto-analyze when component receives content (if available)
  useEffect(() => {
    if (productName && category && existingContent && !hasAnalyzed) {
      handleAnalyzeContent();
    }
  }, [productName, category, existingContent, hasAnalyzed]);

  const handleAnalyzeContent = async () => {
    if (!productName || !category) {
      toast.error('Nome do produto e categoria são necessários para análise.');
      return;
    }

    setIsLoadingSuggestions(true);
    const toastId = toast.loading('A analisar conteúdo e sugerir informações adicionais...');

    try {
      const response = await fetch('/api/suggest-additional-info', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'suggest',
          productName,
          category,
          existingContent
        }),
      });

      const data = await response.json();
      if (!response.ok) throw new Error(data.error || 'Erro ao analisar conteúdo.');

      setSuggestions(data.suggestions);
      setAnalysis(data.analysis);
      setHasAnalyzed(true);
      
      // Auto-select high priority suggestions
      const highPrioritySections = data.suggestions
        .filter((s: AdditionalInfoSuggestion) => s.priority === 'high')
        .map((s: AdditionalInfoSuggestion) => s.category);
      setSelectedSections(highPrioritySections);

      toast.success(`${data.suggestions.length} sugestões encontradas!`, { id: toastId });
    } catch (error) {
      console.error('Erro ao analisar conteúdo:', error);
      toast.error('Falha ao analisar conteúdo. Tente novamente.', { id: toastId });
    } finally {
      setIsLoadingSuggestions(false);
    }
  };

  const handleSectionToggle = (sectionCategory: string, checked: boolean) => {
    setSelectedSections(prev => 
      checked 
        ? [...prev, sectionCategory]
        : prev.filter(s => s !== sectionCategory)
    );
  };

  const handleGenerateContent = async () => {
    if (selectedSections.length === 0) {
      toast.error('Selecione pelo menos uma secção para gerar conteúdo.');
      return;
    }

    setIsGeneratingContent(true);
    const toastId = toast.loading('A gerar informações adicionais...');

    try {
      const response = await fetch('/api/suggest-additional-info', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'generate',
          productName,
          category,
          existingContent,
          selectedSections
        }),
      });

      const data = await response.json();
      if (!response.ok) throw new Error(data.error || 'Erro ao gerar conteúdo.');

      // Combine all generated content into a single formatted string with proper spacing
      const combinedContent = Object.entries(data.content)
        .map(([section, content]) => {
          const suggestion = suggestions.find(s => s.category === section);
          const title = suggestion?.title || section;

          // Format content with proper line breaks and structure for each item
          const formattedContent = (content as string)
            .split('\n')
            .filter(line => line.trim()) // Remove empty lines
            .map(line => {
              const trimmedLine = line.trim();
              // Ensure bullet point formatting
              if (trimmedLine.startsWith('•') || trimmedLine.startsWith('-') || trimmedLine.startsWith('*')) {
                // Remove existing bullet and add consistent bullet
                return `• ${trimmedLine.replace(/^[•\-*]\s*/, '')}`;
              }
              return `• ${trimmedLine}`;
            })
            .join('\n');

          // Create well-structured section with proper spacing and formatting
          return `**${title}:**\n\n${formattedContent}`;
        })
        .join('\n\n\n'); // Triple line break between sections for clear visual separation

      // Apply comprehensive whitespace normalization
      const normalizedContent = normalizeWhitespace(combinedContent);

      // Apply automatic error correction to the normalized content
      const correctedContent = applyTextCorrections(normalizedContent);
      onContentGenerated(correctedContent);
      toast.success('Informações adicionais geradas com sucesso!', { id: toastId });
    } catch (error) {
      console.error('Erro ao gerar conteúdo:', error);
      toast.error('Falha ao gerar informações adicionais.', { id: toastId });
    } finally {
      setIsGeneratingContent(false);
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'text-green-600 dark:text-green-400';
      case 'medium': return 'text-yellow-600 dark:text-yellow-400';
      case 'low': return 'text-gray-600 dark:text-gray-400';
      default: return 'text-gray-600 dark:text-gray-400';
    }
  };

  const getPriorityLabel = (priority: string) => {
    switch (priority) {
      case 'high': return 'Recomendado';
      case 'medium': return 'Útil';
      case 'low': return 'Opcional';
      default: return '';
    }
  };

  if (!productName || !category) {
    return (
      <div className="p-4 bg-gray-50 dark:bg-gray-900/50 rounded-lg border border-gray-200 dark:border-gray-700">
        <div className="flex items-center gap-2 text-gray-600 dark:text-gray-400">
          <Info className="h-4 w-4" />
          <span className="text-sm">Preencha o nome do produto e categoria para ativar as sugestões de IA</span>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Analysis Button */}
      {!hasAnalyzed && (
        <div className="space-y-2">
          {!existingContent && (
            <div className="p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
              <p className="text-xs text-blue-700 dark:text-blue-300">
                💡 <strong>Dica:</strong> Para sugestões mais precisas, gere primeiro a descrição principal.
                Mas também pode obter sugestões básicas apenas com o nome e categoria do produto.
              </p>
            </div>
          )}

          <Button
            onClick={handleAnalyzeContent}
            disabled={isLoadingSuggestions}
            variant="outline"
            className="w-full"
          >
            {isLoadingSuggestions ? (
              <>
                <Loader className="mr-2 h-4 w-4 animate-spin" />
                A analisar conteúdo...
              </>
            ) : (
              <>
                <Sparkles className="mr-2 h-4 w-4" />
                {existingContent ? 'Sugerir Informações Adicionais com IA' : 'Analisar Produto e Sugerir Informações'}
              </>
            )}
          </Button>
        </div>
      )}

      {/* Analysis Results */}
      {analysis && (
        <div className="p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
          <h4 className="text-sm font-medium text-blue-900 dark:text-blue-100 mb-2">
            Análise do Produto
          </h4>
          <div className="text-xs text-blue-700 dark:text-blue-300 space-y-1">
            <p><strong>Tipo:</strong> {analysis.productType}</p>
            {analysis.detectedFeatures.length > 0 && (
              <p><strong>Características detetadas:</strong> {analysis.detectedFeatures.join(', ')}</p>
            )}
          </div>
        </div>
      )}

      {/* Suggestions List */}
      {suggestions.length > 0 && (
        <div className="space-y-3">
          <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300">
            Selecione as informações adicionais a incluir:
          </h4>
          
          <div className="space-y-2">
            {suggestions.map((suggestion) => (
              <div
                key={suggestion.category}
                className="flex items-start space-x-3 p-3 border border-gray-200 dark:border-gray-700 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800/50"
              >
                <Checkbox
                  id={suggestion.category}
                  checked={selectedSections.includes(suggestion.category)}
                  onCheckedChange={(checked) => 
                    handleSectionToggle(suggestion.category, checked as boolean)
                  }
                  className="mt-0.5"
                />
                <div className="flex-1 min-w-0">
                  <div className="flex items-center gap-2 mb-1">
                    <label
                      htmlFor={suggestion.category}
                      className="text-sm font-medium text-gray-700 dark:text-gray-300 cursor-pointer"
                    >
                      {suggestion.title}
                    </label>
                    <span className={`text-xs px-2 py-0.5 rounded-full bg-gray-100 dark:bg-gray-800 ${getPriorityColor(suggestion.priority)}`}>
                      {getPriorityLabel(suggestion.priority)}
                    </span>
                  </div>
                  <p className="text-xs text-gray-600 dark:text-gray-400">
                    {suggestion.description}
                  </p>
                </div>
              </div>
            ))}
          </div>

          {/* Generate Button */}
          <Button
            onClick={handleGenerateContent}
            disabled={isGeneratingContent || selectedSections.length === 0}
            className="w-full"
          >
            {isGeneratingContent ? (
              <>
                <Loader className="mr-2 h-4 w-4 animate-spin" />
                A gerar conteúdo...
              </>
            ) : (
              <>
                <CheckCircle2 className="mr-2 h-4 w-4" />
                Gerar Informações Selecionadas ({selectedSections.length})
              </>
            )}
          </Button>
        </div>
      )}

      {/* Re-analyze Button */}
      {hasAnalyzed && (
        <Button
          onClick={() => {
            setHasAnalyzed(false);
            setSuggestions([]);
            setSelectedSections([]);
            setAnalysis(null);
          }}
          variant="outline"
          size="sm"
          className="w-full"
        >
          <Sparkles className="mr-2 h-3 w-3" />
          Nova Análise
        </Button>
      )}
    </div>
  );
};

export default AdditionalInfoGenerator;
